const { EmbedBuilder, PermissionFlagsBits } = require('discord.js')
const { embedcolor } = require('./../../config.json')
const { ensureGuildData } = require('../../database/global')
const autoreactschema = require('../../database/autoreact')
const { embeds } = require('../../utils/embeds')
const { createPagination, createConfirmation } = require('../../utils/buttons')

module.exports = {
  name: "autoreact",
  aliases: ['react'],
  description: `automatically react to messages containing specific words`,
  usage: '{guildprefix}autoreact add [emoji] word\n{guildprefix}autoreact remove [word]\n{guildprefix}autoreact list\n{guildprefix}autoreact reset',
  run: async(client, message, args) => {

    if (!client.permissions.hasPermission(message, PermissionFlagsBits.ManageChannels, "Manage Channels")) {
      return;
    }

    // Ensure guild data exists in the database
    await ensureGuildData(message.guild.id);

    if (args[0] === 'add' || args[0] === 'create') {

      if (!args[1] || !args[2]) {
        return client.commands.get('help').run(client, message, ['autoreact']);
      }

      const emojiInput = args[1];
      const word = args[2].toLowerCase();

      // Check word length
      if (word.length < 3) {
        return embeds.warn.send(message, 'Trigger word must contain more than 2 characters');
      }

      let emojiId = null;
      let emojiName = null;
      let isCustom = false;

      // Check if it's a custom emoji format <:name:id> or <a:name:id>
      const customEmojiMatch = emojiInput.match(/^<a?:(\w+):(\d+)>$/);

      if (customEmojiMatch) {
        emojiName = customEmojiMatch[1];
        emojiId = customEmojiMatch[2];

        // Check if the emoji exists in the guild
        const emoji = message.guild.emojis.cache.get(emojiId);

        if (!emoji) {
          return embeds.warn.send(message, 'Wrong emote');
        }

        isCustom = true;
      } else {
        // For unicode emojis
        emojiName = emojiInput;
        isCustom = false;
      }

      const existingData = await autoreactschema.findOne({
        GuildID: message.guild.id,
        Word: word,
        EmojiID: emojiId,
        EmojiName: emojiName,
        IsCustom: isCustom
      });

      if (existingData) {
        return embeds.warn.send(message, `Reaction trigger already exists for **${word}**`);
      }

      // Save to database
      const newData = new autoreactschema({
        GuildID: message.guild.id,
        Word: word,
        EmojiID: emojiId,
        EmojiName: emojiName,
        IsCustom: isCustom
      });

      await newData.save();

      const emojiDisplay = isCustom ? `<:${emojiName}:${emojiId}>` : emojiName;
      return embeds.success.send(message, `Created autoreact for **${word}** with ${emojiDisplay}`);

    } else if (args[0] === 'remove' || args[0] === 'delete' || args[0] === 'rm' || args[0] === 'del') {

      const word = args[1];

      if (!word) {
        return client.commands.get('help').run(client, message, ['autoreact']);
      }

      const data = await autoreactschema.findOne({ GuildID: message.guild.id, Word: word.toLowerCase() });

      if (data) {
        await autoreactschema.findOneAndRemove({ GuildID: message.guild.id, Word: word.toLowerCase() });
        return embeds.success.send(message, `Removed autoreact for **${word}**`);
      } else {
        return embeds.warn.send(message, `No autoreact found for **${word}**`);
      }

    } else if (args[0] === 'list') {

      const data = await autoreactschema.find({ GuildID: message.guild.id });

      if (data && data.length > 0) {
        // Clean up invalid emojis before displaying
        const validData = [];
        for (const autoreact of data) {
          if (autoreact.IsCustom) {
            // Check if custom emoji still exists in the guild
            const emoji = message.guild.emojis.cache.get(autoreact.EmojiID);
            if (emoji) {
              validData.push(autoreact);
            } else {
              // Remove invalid emoji from database
              await autoreactschema.findOneAndRemove({ _id: autoreact._id });
            }
          } else {
            // Unicode emojis are always valid
            validData.push(autoreact);
          }
        }

        if (validData.length === 0) {
          return embeds.warn.send(message, 'No autoreacts found');
        }

        // Format page function for pagination
        const formatPage = (pageAutoreacts, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 10;

          const autoreactList = pageAutoreacts.map((autoreact, index) => {
            const number = (startIndex + index + 1).toString().padStart(2, '0');
            const emojiDisplay = autoreact.IsCustom ? 
              `<:${autoreact.EmojiName}:${autoreact.EmojiID}>` : 
              autoreact.EmojiName;
            
            return `\`${number}\` **${autoreact.Word}** → ${emojiDisplay}`;
          }).join('\n');

          return new EmbedBuilder()
            .setColor(embedcolor)
            .setTitle('Autoreacts')
            .setDescription(autoreactList)
            .setFooter({ text: `${validData.length} autoreacts • Page ${currentPage}/${totalPages}` });
        };

        // Use pagination system with 10 items per page
        await createPagination(message, validData, formatPage, 10, 'Autoreacts');
      } else {
        return embeds.warn.send(message, 'No autoreacts found');
      }

    } else if (args[0] === 'reset') {

      const data = await autoreactschema.findOne({ GuildID: message.guild.id });

      if (data) {
        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **remove all autoreacts**?',
          async (interaction) => {
            // On approve: delete all autoreacts
            await autoreactschema.deleteMany({ GuildID: message.guild.id });

            // Edit the confirmation embed to show success message
            const successEmbed = new EmbedBuilder()
              .setColor(embedcolor)
              .setDescription(`✅ <@${message.author.id}>: Removed all autoreacts`);

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } else {
        return embeds.warn.send(message, 'No autoreacts found');
      }

    } else {
      return client.commands.get('help').run(client, message, ['autoreact']);
    }
  }
}
