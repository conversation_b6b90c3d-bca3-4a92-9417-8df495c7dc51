const { EmbedBuilder, PermissionFlagsBits } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const { ensureGuildData } = require('../../database/global')
const autoroleschema = require('../../database/autorole')
const { embeds } = require('../../utils/embeds')
const { createConfirmation } = require('../../utils/buttons')

module.exports = {
  name: "autorole",
  aliases: ['ar'],
  description: `Set up automatic role assign on member join`,
  usage: '{guildprefix}autorole add [role]\n{guildprefix}autorole remove [role] \n{guildprefix}autorole list \n{guildprefix}autorole reset',
  run: async(client, message, args) => {

    if (!client.permissions.hasPermission(message, PermissionFlagsBits.ManageChannels, "Manage Channels")) {
      return;
    }

    await ensureGuildData(message.guild.id);

    if (args[0] === 'add') {

      const roleName = args.slice(1).join(' ');
      const roleResult = message.mentions.roles.first() ?
        { found: true, role: message.mentions.roles.first() } :
        client.findRole(message.guild, roleName);

      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }

      const role = roleResult.role;

      // Optional hierarchy checks (set to false to skip)
      if (!client.permissions.checkUserRoleHierarchy(message, role, true)) return;
      if (!client.permissions.checkBotRoleHierarchy(message, role, true)) return;

      let autoroledata = await autoroleschema.findOne({ GuildID: message.guild.id })

      if (!autoroledata) {
        // Create new autorole document if none exists
        autoroledata = new autoroleschema({
          GuildID: message.guild.id,
          RoleIDs: []
        });
      }

      // Check if role is already in autoroles
      if (autoroledata.RoleIDs.includes(role.id)) {
        return embeds.warn.send(message, `**${role.name}** is already an auto role`);
      }

      // Check if we've reached the 10 role limit
      if (autoroledata.RoleIDs.length >= 10) {
        return embeds.error.send(message, 'You have added max autoroles (10). Please remove some first.');
      }

      // Add the new role
      autoroledata.RoleIDs.push(role.id);
      await autoroledata.save();

      return embeds.success.send(message, `**${role.name}** has been added to auto roles`)



    } else if (args[0] === 'reset') {

      const autoroledata = await autoroleschema.findOne({ GuildID: message.guild.id })

      if (autoroledata && autoroledata.RoleIDs.length > 0) {
        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **remove all auto roles**?',
          async (interaction) => {
            // On approve: delete all auto roles
            await autoroleschema.findOneAndRemove({ GuildID: message.guild.id });

            // Edit the confirmation embed to show success message
            const successEmbed = new EmbedBuilder()
              .setColor(embedcolor)
              .setDescription(`✅ <@${message.author.id}>: Removed all auto roles`);

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } else {
        return embeds.warn.send(message, 'No auto roles found')
      }



    } else if (args[0] === 'list') {

      const autoroledata = await autoroleschema.findOne({ GuildID: message.guild.id })

      if (autoroledata && autoroledata.RoleIDs.length > 0) {

        const roleList = autoroledata.RoleIDs.map((roleId, index) => {
          const role = message.guild.roles.cache.get(roleId);
          const roleName = role ? role.name : 'Unknown Role';
          const roleNumber = (index + 1).toString().padStart(2, '0');
          return `\`${roleNumber}\` **${roleName}** (\`${roleId}\`)`;
        }).join('\n');

        const embed = new EmbedBuilder()
        .setColor(embedcolor)
        .setTitle('Auto Roles')
        .setDescription(roleList)
        .setFooter({ text: `${autoroledata.RoleIDs.length}/10 auto roles` })

        return message.channel.send({ embeds: [embed] })

      } else {
        return embeds.warn.send(message, 'No auto roles found')
      }



    } else if (args[0] === 'remove' || args[0] === 'rm' || args[0] === 'delete' || args[0] === 'del') {

      const autoroledata = await autoroleschema.findOne({ GuildID: message.guild.id })

      const roleName = args.slice(1).join(' ');
      const roleResult = message.mentions.roles.first() ?
        { found: true, role: message.mentions.roles.first() } :
        client.findRole(message.guild, roleName);

      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }

      const role = roleResult.role;

      if (autoroledata && autoroledata.RoleIDs.length > 0) {
        if (!autoroledata.RoleIDs.includes(role.id)) {
          return embeds.error.send(message, `**${role.name}** is not an auto role`);
        }

        // Remove the role from the array
        autoroledata.RoleIDs = autoroledata.RoleIDs.filter(roleId => roleId !== role.id);

        // If no roles left, delete the document
        if (autoroledata.RoleIDs.length === 0) {
          await autoroleschema.findOneAndRemove({ GuildID: message.guild.id });
        } else {
          await autoroledata.save();
        }

        return embeds.success.send(message, `Removed **${role.name}** from auto roles`)
      } else {
        return embeds.error.send(message, `No auto roles found`)
      }

    } else {
      return client.commands.get('help').run(client, message, ['autorole']);
      }
      
  }
}
