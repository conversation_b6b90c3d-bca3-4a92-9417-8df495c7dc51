const { EmbedBuilder, PermissionFlagsBits } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const { ensureGuildData } = require('../../database/global')
const { embeds } = require('../../utils/embedbuilder')
const { createPagination } = require('../../utils/buttons')

module.exports = {
  name: "blacklist",
  aliases: ['ignore', 'bl'],
  description: `blacklist a user within the server`,
  usage: '{guildprefix}blacklist user \n{guildprefix}blacklist user [member] \n{guildprefix}blacklist list',
  run: async(client, message, args) => {


    if (!client.permissions.hasPermission(message, PermissionFlagsBits.Administrator, "Administrator")) {
      return;
    }



    if (args[0] === 'user') {

      const userName = args.slice(1).join(' ');
      let userResult;

      if (message.mentions.members.first()) {
        userResult = { found: true, user: message.mentions.members.first() };
      } else {
        try {
          userResult = client.findUser(message.guild, userName);
        } catch (error) {
          console.error('Error in findUser:', error);
          return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
        }

        // Check if userResult is valid and has the expected structure
        if (!userResult || typeof userResult !== 'object') {
          return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
        }
      }

      if (!userResult.found) {
        return embeds.error.send(message, userResult.error || 'User not found');
      }

      const user = userResult.user;
      if (user.id === client.user.id) return embeds.error.send(message, "you can't blacklist me :clown:")

      // Optional hierarchy checks (set to false to skip)
      if (!client.permissions.checkUserMemberHierarchy(message, user, true)) return;
      if (!client.permissions.checkBotMemberHierarchy(message, user, true)) return;

      

      const data = await ensureGuildData(message.guild.id);

      // Ensure BlacklistedUsers array exists
      if (!data.BlacklistedUsers) {
        data.BlacklistedUsers = []
      }

      if (!data.BlacklistedUsers.includes(user.id)) {

        data.BlacklistedUsers.push(user.id)
        data.save();

        return embeds.success.send(message, `Added **${user.user.displayName}** to blacklist`)

      } else {

        let i = data.BlacklistedUsers.indexOf(`${user.id}`)
        data.BlacklistedUsers.splice(i, 1)
        data.save();

        return embeds.success.send(message, `Removed **${user.user.displayName}** from blacklist`)
      }
      


    } else if (args[0] === 'list') {

      const globaldata = await ensureGuildData(message.guild.id);

      const blacklistedusersarray = globaldata.BlacklistedUsers || []

      if (blacklistedusersarray.length > 0) {
        // Create user objects for pagination
        const blacklistedUsers = blacklistedusersarray.map(userId => {
          const user = message.guild.members.cache.get(userId);
          return {
            id: userId,
            username: user ? user.user.username : 'Unknown User'
          };
        });

        // Format page function for pagination
        const formatPage = (pageUsers, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 8;

          const userList = pageUsers.map((userObj, index) => {
            const number = (startIndex + index + 1).toString().padStart(2, '0');
            return `\`${number}\` **${userObj.username}** (\`${userObj.id}\`)`;
          }).join('\n');

          return new EmbedBuilder()
            .setColor(embedcolor)
            .setTitle('The Blacklisted users')
            .setDescription(userList)
            .setFooter({ text: `${blacklistedusersarray.length} blacklisted users • Page ${currentPage}/${totalPages}` });
        };

        // Use pagination system with 8 items per page
        await createPagination(message, blacklistedUsers, formatPage, 8, 'Blacklisted Users')
      } else {
        return embeds.warn.send(message, `no member is **blacklisted** in this server`)
      }

    } else {
      return client.commands.get('help').run(client, message, ['blacklist']);
    }

  }
}
