const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../utils/embedbuilder');

module.exports = {
  name: "hide",
  aliases: ['hidechannel'],
  description: 'hides the channel for default/selected role',
  usage: '{guildprefix}hide\n{guildprefix}hide @role\n{guildprefix}hide #channel @role',
  run: async(client, message, args) => {

    if (!client.permissions.hasPermission(message, PermissionFlagsBits.ManageChannels, "Manage Channels")) {
      return;
    }

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return embeds.botPermissions.send(message, "Manage Channels");
    }

    let targetChannel = message.channel;
    let targetRole = null;

    if (args.length === 0) {
      targetRole = message.guild.roles.everyone;
    } else if (args.length === 1) {
      const roleResult = client.findRole(message.guild, args[0]);
      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }
      targetRole = roleResult.role;
    } else if (args.length === 2) {
      const channelMention = args[0].match(/^<#(\d+)>$/);
      if (channelMention) {
        targetChannel = message.guild.channels.cache.get(channelMention[1]);
        if (!targetChannel) {
          return embeds.error.send(message, `Channel with ID ${channelMention[1]} not found`);
        }
      } else {
        return embeds.error.send(message, "Please provide a valid channel mention (e.g., #general)");
      }

      const roleResult = client.findRole(message.guild, args[1]);
      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }
      targetRole = roleResult.role;
    } else {
      return client.commands.get('help').run(client, message, ['hide']);
    }

    const currentPerms = targetChannel.permissionsFor(targetRole);
    if (currentPerms && !currentPerms.has(PermissionFlagsBits.ViewChannel)) {
      const roleText = targetRole.id === message.guild.id ? "@everyone" : `**${targetRole.name}**`;
      return embeds.warn.send(message, `**#${targetChannel.name}** is already hidden for ${roleText}`);
    }

    try {
      await targetChannel.permissionOverwrites.edit(targetRole, {
        ViewChannel: false,
      });

      return message.react('🙈');
    } catch (error) {
      console.error('Error hiding channel:', error);
      return embeds.error.send(message, "An error occurred while trying to hide the channel");
    }
  }
}
