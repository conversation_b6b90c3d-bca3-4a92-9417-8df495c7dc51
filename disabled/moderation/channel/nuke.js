const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../utils/embeds');
const { createConfirmation } = require('../../utils/buttons');
const { hasPermission } = require('../../utils/permissions');
const globaldataschema = require('../../database/global');

module.exports = {
  name: "nuke",
  description: 'deletes channel and remakes a new one (admin only)',
  usage: '{guildprefix}nuke',
  run: async(client, message, args) => {

    // Admin permission check
    if (!hasPermission(message, PermissionFlagsBits.Administrator, 'Administrator')) return;

    // Check if bot has required permissions
    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return embeds.botPermissions.send(message, 'Manage Channels');
    }

    // Show confirmation dialog
    const confirmationText = `Are you sure that you want to **nuke** this **channel**?`;

    const result = await createConfirmation(message, confirmationText, async (interaction) => {
      await executeNuke(message, interaction);
    });

    // If declined or timeout, the confirmation system handles cleanup
    if (result === 'declined' || result === 'timeout') {
      return;
    }
  }
}

// Separate function to handle the actual nuke logic
async function executeNuke(message, interaction) {
  try {
    // Store channel information
    const channelId = message.channel.id;
    const channelName = message.channel.name;
    const channelParent = message.channel.parent;
    const channelPosition = message.channel.position;
    const channelTopic = message.channel.topic;
    const channelNsfw = message.channel.nsfw;
    const channelRateLimitPerUser = message.channel.rateLimitPerUser;

    // Check if this channel is used for any server features
    const globalData = await globaldataschema.findOne({ GuildID: message.guild.id });
    const featuresToRestore = [];

    if (globalData) {
      // Check all possible channel features
      if (globalData.WelcomeChannel === channelId) {
        featuresToRestore.push('Welcome Channel');
      }
      if (globalData.GoodbyeChannel === channelId) {
        featuresToRestore.push('Goodbye Channel');
      }
      if (globalData.BoostChannel === channelId) {
        featuresToRestore.push('Boost Channel');
      }
      if (globalData.LogChannel === channelId) {
        featuresToRestore.push('Log Channel');
      }
      if (globalData.ModLogChannel === channelId) {
        featuresToRestore.push('Mod Log Channel');
      }
      if (globalData.AutoModChannel === channelId) {
        featuresToRestore.push('AutoMod Channel');
      }
      // Add any other channel features you have in your database
    }

    // Clone the channel
    const newChannel = await message.channel.clone({
      name: channelName,
      topic: channelTopic,
      nsfw: channelNsfw,
      rateLimitPerUser: channelRateLimitPerUser,
      reason: `Channel nuked by ${message.author.tag}`
    });

    // Set parent and position
    if (channelParent) {
      await newChannel.setParent(channelParent.id);
    }
    await newChannel.setPosition(channelPosition);

    // Delete the old channel
    await message.channel.delete(`Channel nuked by ${message.author.tag}`);

    // Restore server features if any were using this channel
    if (featuresToRestore.length > 0 && globalData) {
      const updateData = {};

      if (globalData.WelcomeChannel === channelId) {
        updateData.WelcomeChannel = newChannel.id;
      }
      if (globalData.GoodbyeChannel === channelId) {
        updateData.GoodbyeChannel = newChannel.id;
      }
      if (globalData.BoostChannel === channelId) {
        updateData.BoostChannel = newChannel.id;
      }
      if (globalData.LogChannel === channelId) {
        updateData.LogChannel = newChannel.id;
      }
      if (globalData.ModLogChannel === channelId) {
        updateData.ModLogChannel = newChannel.id;
      }
      if (globalData.AutoModChannel === channelId) {
        updateData.AutoModChannel = newChannel.id;
      }

      // Update the database with new channel ID
      await globaldataschema.updateOne(
        { GuildID: message.guild.id },
        { $set: updateData }
      );
    }

    await newChannel.send('first');

  } catch (error) {


    // Try to send error in current channel if it still exists
    try {
      embeds.error.send(message, 'An error occurred while trying to nuke this channel.');
    } catch (sendError) {

    }
  }
}