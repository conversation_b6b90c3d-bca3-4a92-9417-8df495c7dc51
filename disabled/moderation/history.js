const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { hasPermission } = require('../../utils/permissions');
const { embeds } = require('../../utils/embeds');
const { createPagination } = require('../../utils/buttons');
const { 
  getModerationHistory, 
  getModerationCase, 
  getModerationHistoryByAction,
  removeModerationCase,
  removeAllModerationCases 
} = require('../../database/moderation');
const { embedcolor } = require('./../../config.json');

module.exports = {
  name: "history",
  description: `view and manage moderation history`,
  usage: '{guildprefix}history [user]\n{guildprefix}history [user] [action]\n{guildprefix}history view [caseid]\n{guildprefix}history remove [user] [caseid]\n{guildprefix}history removeall [user]',
  run: async(client, message, args) => {
    
    // Permission checks first
    if (!hasPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    // No arguments - show help
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['history']);
    }

    // VIEW SUBCOMMAND
    if (args[0] === 'view') {
      if (!args[1]) {
        return client.commands.get('help').run(client, message, ['history']);
      }

      const caseId = parseInt(args[1]);
      if (isNaN(caseId)) {
        return embeds.error.send(message, 'Please provide a valid case ID number');
      }

      try {
        const moderationCase = await getModerationCase(message.guild.id, caseId);
        
        if (!moderationCase) {
          return embeds.error.send(message, `Case #${caseId} not found`);
        }

        // Get user and moderator info
        const targetUser = await client.users.fetch(moderationCase.userId).catch(() => null);
        const moderator = await client.users.fetch(moderationCase.moderatorId).catch(() => null);
        
        const timestamp = Math.floor(moderationCase.timestamp.getTime() / 1000);
        
        const embed = new EmbedBuilder()
          .setColor(embedcolor)
          .setTitle(`Case #${moderationCase.caseId} | ${moderationCase.action}`)
          .addFields(
            { name: 'User', value: targetUser ? `${targetUser.username} (${targetUser.id})` : `Unknown User (${moderationCase.userId})`, inline: true },
            { name: 'Moderator', value: moderator ? `${moderator.username} (${moderator.id})` : `Unknown User (${moderationCase.moderatorId})`, inline: true },
            { name: 'Timestamp', value: `<t:${timestamp}:f>`, inline: true },
            { name: 'Reason', value: moderationCase.reason, inline: false }
          );

        if (moderationCase.duration) {
          embed.addFields({ name: 'Duration', value: moderationCase.duration, inline: true });
        }

        return message.channel.send({ embeds: [embed] });

      } catch (error) {
        console.error('History view error:', error);
        return embeds.error.send(message, 'An error occurred while retrieving the case');
      }
    }

    // REMOVE SUBCOMMAND
    if (args[0] === 'remove') {
      if (!args[1] || !args[2]) {
        return client.commands.get('help').run(client, message, ['history']);
      }

      // Need Manage Guild permission to remove cases
      if (!hasPermission(message, PermissionFlagsBits.ManageGuild, 'Manage Guild')) return;

      const userResult = client.findUser(message.guild, args[1]);
      if (!userResult.found) {
        return embeds.error.send(message, userResult.error || 'User not found');
      }

      const caseId = parseInt(args[2]);
      if (isNaN(caseId)) {
        return embeds.error.send(message, 'Please provide a valid case ID number');
      }

      try {
        const moderationCase = await getModerationCase(message.guild.id, caseId);
        
        if (!moderationCase) {
          return embeds.error.send(message, `Case #${caseId} not found`);
        }

        if (moderationCase.userId !== userResult.user.user.id) {
          return embeds.error.send(message, `Case #${caseId} does not belong to **${userResult.user.user.username}**`);
        }

        await removeModerationCase(message.guild.id, caseId);
        return embeds.success.send(message, `Removed case #${caseId} for **${userResult.user.user.username}**`);

      } catch (error) {
        console.error('History remove error:', error);
        return embeds.error.send(message, 'An error occurred while removing the case');
      }
    }

    // REMOVEALL SUBCOMMAND
    if (args[0] === 'removeall') {
      if (!args[1]) {
        return client.commands.get('help').run(client, message, ['history']);
      }

      // Need Manage Guild permission to remove cases
      if (!hasPermission(message, PermissionFlagsBits.ManageGuild, 'Manage Guild')) return;

      const userResult = client.findUser(message.guild, args[1]);
      if (!userResult.found) {
        return embeds.error.send(message, userResult.error || 'User not found');
      }

      try {
        const result = await removeAllModerationCases(message.guild.id, userResult.user.user.id);
        
        if (result.deletedCount === 0) {
          return embeds.warn.send(message, `**${userResult.user.user.username}** has no moderation history to remove`);
        }

        return embeds.success.send(message, `Removed **${result.deletedCount}** case${result.deletedCount === 1 ? '' : 's'} for **${userResult.user.user.username}**`);

      } catch (error) {
        console.error('History removeall error:', error);
        return embeds.error.send(message, 'An error occurred while removing cases');
      }
    }

    // Check if first argument is an action type (like ;history kicks)
    const actionTypes = ['warn', 'mute', 'kick', 'ban', 'jail', 'unmute', 'unban', 'unjail', 'warns', 'kicks', 'bans', 'mutes', 'jails'];
    if (actionTypes.includes(args[0].toLowerCase())) {
      return client.commands.get('help').run(client, message, ['history']);
    }

    // USER HISTORY (with optional action filter)
    const userResult = client.findUser(message.guild, args[0]);
    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    // Check if viewing someone else's history
    if (userResult.user.user.id !== message.author.id) {
      if (!hasPermission(message, PermissionFlagsBits.ManageGuild, 'Manage Guild')) return;
    }

    const user = userResult.user;
    const actionFilter = args[1] ? args[1].toLowerCase() : null;

    try {
      let moderationHistory;
      
      if (actionFilter && actionTypes.includes(actionFilter)) {
        moderationHistory = await getModerationHistory(message.guild.id, user.user.id, actionFilter);
      } else {
        moderationHistory = await getModerationHistory(message.guild.id, user.user.id);
      }

      if (moderationHistory.length === 0) {
        const filterText = actionFilter ? ` ${actionFilter}` : '';
        return embeds.warn.send(message, `**${user.user.username}** has no${filterText} moderation history`);
      }

      // Format page function for pagination (3 cases per page)
      const formatPage = (pageItems, currentPage, totalPages) => {
        const historyList = pageItems.map((case_) => {
          const timestamp = Math.floor(case_.timestamp.getTime() / 1000);
          const moderator = message.guild.members.cache.get(case_.moderatorId);
          const moderatorName = moderator ? `${moderator.user.username}(${moderator.user.id})` : `Unknown User(${case_.moderatorId})`;
          
          return [
            `**case log #${case_.caseId} | ${case_.action}**`,
            `**Punished**: <t:${timestamp}:f>`,
            `**Moderator**: ${moderatorName}`,
            `**Reason**: ${case_.reason}`,
            '' // Empty line for spacing
          ].join('\n');
        }).join('\n');

        const filterText = actionFilter ? ` ${actionFilter}` : '';
        return new EmbedBuilder()
          .setColor(embedcolor)
          .setTitle(`${filterText ? actionFilter.charAt(0).toUpperCase() + actionFilter.slice(1) : 'Moderation'} history for ${user.user.username}`)
          .setDescription(historyList)
          .setFooter({ text: `Page ${currentPage}/${totalPages} • ${moderationHistory.length} case${moderationHistory.length === 1 ? '' : 's'} total` });
      };

      // Use pagination system with 3 items per page
      await createPagination(message, moderationHistory, formatPage, 3, `History for ${user.user.username}`);

    } catch (error) {
      console.error('History command error:', error);
      embeds.error.send(message, 'An error occurred while retrieving moderation history.');
    }
  }
};
