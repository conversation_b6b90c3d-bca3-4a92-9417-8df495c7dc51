const { EmbedBuilder } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const { createPagination } = require('../../utils/buttons')

module.exports = {
  name: "inrole",
  aliases: ['members', 'rolemembers'],
  description: `get a list of users in a specific role`,
  usage: '{guildprefix}inrole [role]',
  run: async(client, message, args) => {

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['inrole']);
    }

    // Use global role finder
    const roleResult = client.findRole(message.guild, args.join(' '));

    if (!roleResult.found) {
      return message.channel.send(roleResult.error);
    }

    const role = roleResult.role;

    let members = message.guild.members.cache.filter(member => {
      return member.roles.cache.has(role.id);
    }).map(member => {
      return member.user;
    })

    if (members.length < 1) return message.channel.send(`**${role.name}** has no members in the role`)

    // Format page function for pagination
    const formatPage = (pageMembers, currentPage, totalPages) => {
      const startIndex = (currentPage - 1) * 10;

      const memberList = pageMembers.map((member, index) => {
        const memberNumber = (startIndex + index + 1).toString().padStart(2, '0');
        return `\`${memberNumber}\` **${member.username}** (\`${member.id}\`)`;
      }).join('\n');

      return new EmbedBuilder()
        .setColor(embedcolor)
        .setTitle(`Members in ${role.name}`)
        .setDescription(memberList)
        .setFooter({ text: `${role.members.size} members • Page ${currentPage}/${totalPages}` });
    };

    // Use pagination system
    await createPagination(message, members, formatPage, 10, `${role.name} Members`)
  }
}