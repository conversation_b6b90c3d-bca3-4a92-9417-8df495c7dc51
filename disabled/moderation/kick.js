const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../utils/embedbuilder')
const { hasPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy, checkDiscordActionable } = require('../../utils/permissions')
const { addModerationCase } = require('../../database/moderation');

module.exports = {
  name: "kick",
  aliases: ['kick', 'boot'],
  description: 'Kicks the mentioned user from the server',
  usage: '{guildprefix}kick [user]\n{guildprefix}kick [user] [reason]',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasPermission(message, PermissionFlagsBits.KickMembers, 'Kick Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.KickMembers)) {
      return embeds.botPermissions.send(message, 'Kick Members');
    }

    // Check if user provided input
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['kick']);
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    if (!checkSelfAction(message, user, 'kick')) return;
    if (!checkBotAction(message, user)) return;

    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    // Check if Discord allows the kick action
    if (!checkDiscordActionable(message, user, 'kick')) return;

    // Perform the kick
    try {
      await user.kick(`${reason} | Kicked by: ${message.author.tag}`);

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'kick',
          reason
        );
      } catch (historyError) {
        console.error('Error adding kick to moderation history:', historyError);
        // Don't fail the command if history logging fails
      }

      embeds.success.send(message, `**${user.user.tag}** has been **kicked** for ${reason}`);
    } catch (error) {
      console.error('Kick error:', error);
      embeds.error.send(message, 'An error occurred while trying to kick this user.');
    }
  }
}