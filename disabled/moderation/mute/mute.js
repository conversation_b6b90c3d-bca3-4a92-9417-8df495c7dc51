const { PermissionFlagsBits } = require("discord.js");
const { hasPermission, checkSelfAction, checkBotAction, checkDiscordActionable } = require('../../utils/permissions')
const { embeds } = require('../../utils/embedbuilder');
const { addModerationCase } = require('../../database/moderation');
const ms = require('ms');

module.exports = {
  name: "mute",
  aliases: ['m', 'timeout'],
  description: `timeout a user using Discord's built-in timeout feature`,
  usage: '{guildprefix}mute [user] [duration] [reason]\n{guildprefix}mute @user 10m spam\n{guildprefix}mute @user 2d breaking rules\n{guildprefix}mute @user 30s quick timeout',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.botPermissions.send(message, 'Moderate Members');
    }

    // Check if user provided input
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['mute']);
    }

    // Use universal user finder with error handling
    let userResult;
    try {
      userResult = client.findUser(message.guild, args[0]);
    } catch (error) {
      console.error('Error in findUser:', error);
      return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
    }

    // Check if userResult is valid and has the expected structure
    if (!userResult || typeof userResult !== 'object') {
      return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
    }

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    const user = userResult.user;

    // Check self action
    if (!checkSelfAction(message, user.user)) return;

    // Check bot action
    if (!checkBotAction(message, user.user)) return;

    // Check if user is actionable (hierarchy and Discord restrictions)
    if (!checkDiscordActionable(message, user, 'timeout')) return;

    // Check if user is already timed out
    if (user.communicationDisabledUntil && user.communicationDisabledUntil > new Date()) {
      return embeds.warn.send(message, `**${user.user.username}** is already timed out`);
    }

    // Parse duration and reason
    let duration = ms('5m'); // Default 5 minutes
    let reason = 'reasons';

    if (args[1]) {
      const parsedDuration = ms(args[1]);
      if (parsedDuration && parsedDuration > 0) {
        // Discord's maximum timeout is 28 days
        const maxTimeout = ms('28d');
        duration = Math.min(parsedDuration, maxTimeout);
        reason = args.slice(2).join(' ') || 'reasons';
      } else {
        // If second arg isn't a valid duration, treat it as part of reason
        reason = args.slice(1).join(' ');
      }
    }

    try {
      await user.timeout(duration, reason);

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'mute',
          reason,
          ms(duration, { long: true }) // Store duration in readable format
        );
      } catch (historyError) {
        console.error('Error adding mute to moderation history:', historyError);
        // Don't fail the command if history logging fails
      }

      return embeds.success.send(message, `**${user.user.username}** has been **muted** for ${ms(duration, { long: true })} for ${reason}`);

    } catch (error) {
      console.error('Error timing out user:', error);
      return embeds.error.send(message, 'Failed to timeout user. Please check my permissions and role hierarchy.');
    }
  }
}