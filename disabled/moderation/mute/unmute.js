const { EmbedBuilder, PermissionFlagsBits } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const { hasPermission, checkSelfAction, checkBotAction, checkDiscordActionable } = require('../../utils/permissions')
const { embeds } = require('../../utils/embeds');
const { addModerationCase } = require('../../database/moderation');

module.exports = {
  name: "unmute",
  aliases: ['untimeout'],
  description: `remove timeout from a user using Discord's built-in timeout feature`,
  usage: '{guildprefix}unmute [user] [reason]\n{guildprefix}unmute @user appeal accepted',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.botPermissions.send(message, 'Moderate Members');
    }

    // Check if user provided input
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['unmute']);
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    const user = userResult.user;

    // Check self action
    if (!checkSelfAction(message, user.user)) return;

    // Check bot action
    if (!checkBotAction(message, user.user)) return;

    // Check if user is actionable (hierarchy and Discord restrictions)
    if (!checkDiscordActionable(message, user, 'untimeout')) return;

    // Check if user is actually timed out
    if (!user.communicationDisabledUntil || user.communicationDisabledUntil <= new Date()) {
      return embeds.warn.send(message, `**${user.user.username}** is not timed out`);
    }

    const reason = args.slice(1).join(' ') || 'reasons';

    try {
      await user.timeout(null, reason); // null removes the timeout

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'unmute',
          reason
        );
      } catch (historyError) {
        console.error('Error adding unmute to moderation history:', historyError);
        // Don't fail the command if history logging fails
      }

      return embeds.success.send(message, `**${user.user.username}** has been **unmuted** for ${reason}`);

    } catch (error) {
      console.error('Error removing timeout:', error);
      return embeds.error.send(message, 'Failed to remove timeout. Please check my permissions and role hierarchy.');
    }
  }
}