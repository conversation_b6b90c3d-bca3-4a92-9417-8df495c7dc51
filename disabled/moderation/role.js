const { ensureGuildData } = require('../../database/global')
const { embeds } = require('../../utils/embedbuilder')
const { hasPermission, checkUserRoleHierarchy, checkBotRoleHierarchy, checkUserMemberHierarchy, checkSelfAction, checkBotAction, checkDiscordActionable, PermissionFlagsBits } = require('../../utils/permissions')
const Color = require('color')

module.exports = {
  name: "role",
  description: `Comprehensive role management system`,
  aliases: ['r'],
  usage: '{guildprefix}role [member] [role]\n{guildprefix}role create [name]\n{guildprefix}role delete [role]\n{guildprefix}role rename [role] [new name]\n{guildprefix}role color [role] [color]\n{guildprefix}role icon [role] [emoji]',
  run: async(client, message, args) => {

    // Permission checks first using utils/permissions.js
    if (!hasPermission(message, PermissionFlagsBits.ManageRoles, 'Manage Roles')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
      return embeds.botPermissions.send(message, 'Manage Roles');
    }

    await ensureGuildData(message.guild.id);

    // CREATE ROLE SUBCOMMAND
    if (args[0] === 'create' || args[0] === 'c') {
      const roleName = args.slice(1).join(' ');

      if (!roleName) {
        return client.commands.get('help').run(client, message, ['role']);
      }

      try {
        const newRole = await message.guild.roles.create({
          name: roleName,
          reason: `Role created by ${message.author.tag}`
        });

        return embeds.success.send(message, `Created role **${newRole.name}**`);
      } catch (error) {
        return embeds.error.send(message, 'Failed to create role. Please check my permissions.');
      }
    }

    // DELETE ROLE SUBCOMMAND
    if (args[0] === 'delete' || args[0] === 'del') {
      if (!args[1]) {
        return client.commands.get('help').run(client, message, ['role']);
      }

      const roleResult = client.findRole(message.guild, args.slice(1).join(' '));

      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }

      const role = roleResult.role;

      // Check role hierarchy using utils/permissions.js
      if (!checkUserRoleHierarchy(message, role)) return;
      if (!checkBotRoleHierarchy(message, role)) return;

      try {
        const roleName = role.name;
        await role.delete(`Role deleted by ${message.author.tag}`);
        return embeds.success.send(message, `Deleted role **${roleName}**`);
      } catch (error) {
        return embeds.error.send(message, 'Failed to delete role. Please check my permissions.');
      }
    }

    // RENAME ROLE SUBCOMMAND
    if (args[0] === 'rename') {
      if (!args[1] || !args[2]) {
        return client.commands.get('help').run(client, message, ['role']);
      }

      let roleResult = null;
      let newName = '';

      // Try different splits to find the role
      for (let i = 1; i < args.length - 1; i++) {
        const possibleRoleName = args.slice(1, i + 1).join(' ');
        const testResult = client.findRole(message.guild, possibleRoleName);

        if (testResult.found) {
          roleResult = testResult;
          newName = args.slice(i + 1).join(' ');
          break;
        }
      }

      if (!roleResult || !newName) {
        return embeds.error.send(message, 'Could not find the role to rename. Usage: `,role rename <old role name> <new role name>`');
      }

      const role = roleResult.role;

      // Check role hierarchy using utils/permissions.js
      if (!checkUserRoleHierarchy(message, role)) return;
      if (!checkBotRoleHierarchy(message, role)) return;

      try {
        const oldName = role.name;
        await role.setName(newName, `Role renamed by ${message.author.tag}`);
        return embeds.success.send(message, `Renamed **${oldName}** to **${newName}**`);
      } catch (error) {
        return embeds.error.send(message, 'Failed to rename role. Please check my permissions.');
      }
    }

    // COLOR ROLE SUBCOMMAND
    if (args[0] === 'color') {
      if (!args[1] || !args[2]) {
        return client.commands.get('help').run(client, message, ['role']);
      }

      // For color command, try different parsing approaches
      let role = null;
      let colorArg = '';

      // First try: last argument is color, everything else is role name
      colorArg = args[args.length - 1];
      let roleName = args.slice(1, -1).join(' ');

      let roleResult = client.findRole(message.guild, roleName);

      // If that doesn't work, try: second argument is role, third is color
      if (!roleResult.found && args.length === 3) {
        roleName = args[1];
        colorArg = args[2];
        roleResult = client.findRole(message.guild, roleName);
      }

      if (!roleResult.found) {
        return embeds.error.send(message, `Could not find role. Usage: \`,role color <role name> <color>\``);
      }

      role = roleResult.role;

      // Check role hierarchy using utils/permissions.js
      if (!checkUserRoleHierarchy(message, role)) return;
      if (!checkBotRoleHierarchy(message, role)) return;

      try {
        // Use the color library to parse any color format
        const parsedColor = Color(colorArg);
        const hexColor = parsedColor.hex();

        await role.setColor(hexColor, `Role color changed by ${message.author.tag}`);
        return embeds.success.send(message, `Changed **${role.name}** color to **${colorArg}** (${hexColor})`);
      } catch (error) {
        return embeds.error.send(message, `Invalid color **${colorArg}**. Use hex codes (#FF0000), RGB (rgb(255,0,0)), HSL (hsl(0,100%,50%)), or color names (red, blue, white, etc.)`);
      }
    }

    // ICON ROLE SUBCOMMAND
    if (args[0] === 'icon') {
      if (!args[1] || !args[2]) {
        return client.commands.get('help').run(client, message, ['role']);
      }

      // Find role name (everything except the last argument which is the emoji)
      const emojiArg = args[args.length - 1];
      const roleName = args.slice(1, -1).join(' ');

      const roleResult = client.findRole(message.guild, roleName);

      if (!roleResult.found) {
        return embeds.error.send(message, roleResult.error);
      }

      const role = roleResult.role;
      let emoji = emojiArg;

      // Check role hierarchy using utils/permissions.js
      if (!checkUserRoleHierarchy(message, role)) return;
      if (!checkBotRoleHierarchy(message, role)) return;

      // Handle custom emoji format :name: by finding it in the guild
      if (emoji.startsWith(':') && emoji.endsWith(':')) {
        const emojiName = emoji.slice(1, -1);
        const guildEmoji = message.guild.emojis.cache.find(e => e.name === emojiName);
        if (guildEmoji) {
          emoji = guildEmoji.id;
        }
      }

      try {
        await role.setIcon(emoji, `Role icon changed by ${message.author.tag}`);
        return embeds.success.send(message, `Changed **${role.name}** icon to ${emojiArg}`);
      } catch (error) {
        return embeds.error.send(message, 'Invalid emoji or failed to change role icon. Note: Role icons require server boost level 2+');
      }
    }

    // DEFAULT ROLE TOGGLE (no subcommand)
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['role']);
    }

    // Main role toggle functionality: ,role (member) <role name>
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error);
    }

    const user = userResult.user;
    const roleResult = client.findRole(message.guild, args.slice(1).join(' '));

    if (!roleResult.found) {
      return embeds.error.send(message, roleResult.error);
    }

    const role = roleResult.role;

    // Check if user can perform action on target using utils/permissions.js
    if (!checkSelfAction(message, user.user, 'manage roles for')) return;
    if (!checkBotAction(message, user.user)) return;
    if (!checkUserMemberHierarchy(message, user)) return;

    // Check if Discord allows role management on this member
    if (!checkDiscordActionable(message, user, 'manage roles')) return;

    // Check role hierarchy using utils/permissions.js
    if (!checkUserRoleHierarchy(message, role)) return;
    if (!checkBotRoleHierarchy(message, role)) return;

    try {
      if (user.roles.cache.has(role.id)) {
        // Remove role
        await user.roles.remove(role.id, `Role removed by ${message.author.tag}`);
        return embeds.success.send(message, `Removed **${role.name}** from **${user.user.username}**`);
      } else {
        // Add role
        await user.roles.add(role.id, `Role added by ${message.author.tag}`);
        return embeds.success.send(message, `Added **${role.name}** to **${user.user.username}**`);
      }
    } catch (error) {
      console.error('Role management error:', error);
      if (error.code === 50013) {
        return embeds.error.send(message, `I don't have permission to manage **${user.user.username}**'s roles or the **${role.name}** role. Please check my role hierarchy and permissions.`);
      }
      return embeds.error.send(message, 'Failed to modify user roles. Please check my permissions.');
    }
  }
}