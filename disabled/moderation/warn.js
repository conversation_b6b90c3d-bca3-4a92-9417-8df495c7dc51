const { PermissionFlagsBits } = require("discord.js");
const { hasPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../utils/permissions');
const { embeds } = require('../../utils/embedbuilder');
const { addModerationCase } = require('../../database/moderation');

module.exports = {
  name: "warn",
  description: `warn a user and add it to their moderation history`,
  usage: '{guildprefix}warn [user]\n{guildprefix}warn [user] [reason]',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.botPermissions.send(message, 'Moderate Members');
    }

    // Check if user provided input
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['warn']);
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    // Self-action check
    if (!checkSelfAction(message, user.user, 'warn')) return;

    // Bot-action check
    if (!checkBotAction(message, user.user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      // Add moderation case to database
      const moderationCase = await addModerationCase(
        message.guild.id,
        user.user.id,
        message.author.id,
        'warn',
        reason
      );

      // Send success message
      embeds.success.send(message, `**${user.user.username}** has been **warned** for ${reason}`);

    } catch (error) {

      embeds.error.send(message, `An error occurred while warning **${user.user.username}**`);
    }
  }
};
