const { PermissionFlagsBits } = require('discord.js');
const { hasPermission } = require('../../utils/permissions');

module.exports = {
  name: "warns",
  description: `view warn history for a user`,
  usage: '{guildprefix}warns [user]',
  run: async(client, message, args) => {


    if (!hasPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['warns']);
    }

    const historyCommand = client.commands.get('history');
    if (historyCommand) {

      return historyCommand.run(client, message, [args[0], 'warn']);
    } else {

      return message.channel.send('History command not found. Please contact an administrator.');
    }
  }
};
