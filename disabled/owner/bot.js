const { ownerid } = require('../../config.json');
const { embeds } = require('../../utils/embedbuilder');
const { ActivityType } = require('discord.js');

module.exports = {
  name: "setbot",
  aliases: ["settings"],
  description: "Manage bot's status",
  usage: "{guildprefix}setbot status <playing|watching|listening|competing> <text>",
  run: async (client, message, args) => {

    if (message.author.id !== ownerid) {
      return;
    }

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['setbot']);
    }

    const subcommand = args[0].toLowerCase();

    if (subcommand === 'status') {
      const activityType = args[1]?.toLowerCase();
      const statusText = args.slice(2).join(" ");
      return await handleStatus(client, message, activityType, statusText);
    } else {
      return client.commands.get('help').run(client, message, ['setbot']);
    }
  }
};

// Handle status changes
async function handleStatus(client, message, activityType, statusText) {
  if (!activityType || !statusText) {
    return embeds.warn.send(message, 'Usage: `,setbot status <playing|watching|listening|competing> <text>`');
  }

  // Map activity types to Discord ActivityType enum
  const activityTypes = {
    'playing': ActivityType.Playing,
    'watching': ActivityType.Watching,
    'listening': ActivityType.Listening,
    'competing': ActivityType.Competing
  };

  const selectedType = activityTypes[activityType];

  if (!selectedType) {
    return embeds.warn.send(message, 'Invalid activity type! Use: `playing`, `watching`, `listening`, or `competing`');
  }

  try {
    client.user.setPresence({
      activities: [{
        name: statusText,
        type: selectedType
      }]
    });

    const typeText = activityType.charAt(0).toUpperCase() + activityType.slice(1);
    return embeds.success.send(message, `Set bot status to **${typeText}** ${statusText}`);
  } catch (err) {
    console.error('Error setting status:', err);
    return embeds.error.send(message, `There was an error: ${err.message}`);
  }
}
