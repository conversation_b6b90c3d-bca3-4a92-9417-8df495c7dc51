const { Client, Message } = require("discord.js");
const { ownerid } = require('../../config.json');
const { embeds } = require('../../utils/embeds');

module.exports = {
    name: 'leave',
    description: 'Forces the bot to leave the current server',
    usage: "{guildprefix}leave [server id]",

    run: async (client, message, args) => {
        if (message.author.id !== ownerid) return;

        if (!message.guild) {
            return embeds.warn.send(message, 'This command can only be used in a server.');
        }

        try {
            await embeds.success.send(message, 'awh man, alr **leaving** now..');
            await message.guild.leave();
        } catch (error) {

            return embeds.error.send(message, `An error occurred while trying to leave the server: ${error.message}`);
        }
    },
};