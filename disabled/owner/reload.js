const { Client, Message } = require("discord.js");
const { ownerid } = require('../../config.json');
const { embeds } = require('../../utils/embeds');
const glob = require("glob");

module.exports = {
  name: "reload",
  description: 'Reloads all commands',
  usage: "{guildprefix}reload",
  cooldowns: 3600,

  run: async (client, message, args) => {
    if (message.author.id !== ownerid) return;
    
    client.commands.clear()
    glob(`${__dirname}/../**/*.js` , async (err, filePaths) => {
        if (err) return;
        filePaths.forEach((file) => {
    delete require.cache[require.resolve(file)];

    const pull = require(file);
    if(pull.name) {

        client.commands.set(pull.name, pull);
    }

    if(pull.aliases && Array.isArray(pull.aliases)) {
        pull.aliases.forEach((alias) => {
            
        client.aliases.set(alias, pull.name);
        });
    }
        }); 
    });

    return embeds.success.send(message, 'Successfully **reloaded all** commands and funcations')
  }
}
