// Message handler for command processing and autoreact

module.exports = {
  name: 'messageCreate',
  async execute(message) {
    // Skip bots
    if (message.author.bot) return;

    // Skip if no guild
    if (!message.guild) return;

    // Process autoreacts first
    await processAutoreact(message);

    // Process commands
    await processCommand(message);
  },
};

// AFK system temporarily disabled - missing database schema

async function processAutoreact(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const { Autoreact } = require('../../database');

    // Get all autoreacts for this guild
    const autoreacts = await Autoreact.find({ GuildID: message.guild.id });

    if (!autoreacts || autoreacts.length === 0) return;

    const messageContent = message.content.toLowerCase();

    // Check each autoreact trigger
    for (const autoreact of autoreacts) {
      // Check if the message contains the trigger word
      if (messageContent.includes(autoreact.Word.toLowerCase())) {
        try {
          let emojiToReact;

          if (autoreact.IsCustom) {
            // For custom emojis, check if it still exists in the guild
            const emoji = message.guild.emojis.cache.get(autoreact.EmojiID);
            if (emoji) {
              emojiToReact = emoji;
            } else {
              // Remove invalid emoji from database
              await Autoreact.findOneAndRemove({ _id: autoreact._id });
              continue;
            }
          } else {
            // For unicode emojis
            emojiToReact = autoreact.EmojiName;
          }

          // React to the message
          await message.react(emojiToReact);
        } catch (error) {
          // Silent fail for individual reactions
          // Could be due to missing permissions, invalid emoji, etc.
        }
      }
    }
  } catch (error) {
    // Silent fail for autoreact system
  }
}

async function processCommand(message) {
  try {
    // Process commands with new prefix system
    const { processCommand } = require('../../utils/commandProcessor');
    await processCommand(message, false);
  } catch (error) {
    // Silent fail for commands
  }
}


