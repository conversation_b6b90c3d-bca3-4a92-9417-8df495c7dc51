const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');

module.exports = {
  name: 'guildMemberAdd',
  async execute(member, client) {
    try {
      // Skip bots - they don't get welcome messages
      if (member.user.bot) {

        return;
      }
      
      // Get guild data to check for welcome message configuration
      const guildData = await ensureGuildData(member.guild.id);
      
      // Check if welcome message is configured
      if (!guildData.WelcomeChannel || !guildData.WelcomeMessage) {
        return; // No welcome message configured
      }
      
      // Get the welcome channel
      const welcomeChannel = member.guild.channels.cache.get(guildData.WelcomeChannel);
      if (!welcomeChannel) {
        return;
      }
      
      // Process and send the welcome message with variables
      try {
        EmbedVariableProcessor.process(welcomeChannel, guildData.WelcomeMessage, {
          user: member.user,
          guild: member.guild,
          channel: welcomeChannel
        });
      } catch (error) {

        // Fallback to simple text message
        welcomeChannel.send(`Welcome ${member}!`);
      }
      
    } catch (error) {

    }
  },
};
